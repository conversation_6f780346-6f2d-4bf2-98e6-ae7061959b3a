<% content_for :title, "Edit #{@ai_provider_configuration.provider_name.titleize} Configuration" %>

<!-- Modern AI Provider Configuration Edit Page -->
<div class="min-h-screen bg-gray-50" data-controller="ai-provider-configuration-form">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

    <!-- Breadcrumb Navigation -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
      <ol role="list" class="flex items-center space-x-4">
        <li>
          <%= link_to admin_ai_provider_configurations_path,
              class: "text-gray-500 hover:text-gray-700 transition-colors duration-200 flex items-center" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
            </svg>
            <span class="text-sm font-medium">AI Provider Configurations</span>
          <% end %>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="flex-shrink-0 h-4 w-4 text-gray-400 mx-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <%= link_to admin_ai_provider_configuration_path(@ai_provider_configuration),
                class: "text-gray-500 hover:text-gray-700 transition-colors duration-200" do %>
              <span class="text-sm font-medium"><%= @ai_provider_configuration.provider_name.titleize %></span>
            <% end %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="flex-shrink-0 h-4 w-4 text-gray-400 mx-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <span class="text-sm font-medium text-gray-900">Edit Configuration</span>
          </div>
        </li>
      </ol>
    </nav>

    <!-- Page Header with Provider Info -->
    <div class="bg-white rounded-xl shadow-sm p-6 mb-8">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div class="flex items-start space-x-4 mb-4 lg:mb-0">
          <!-- Provider Icon -->
          <div class="flex-shrink-0">
            <div class="w-16 h-16 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl flex items-center justify-center">
              <%= provider_icon(@ai_provider_configuration.provider_name) %>
            </div>
          </div>

          <!-- Provider Details -->
          <div class="flex-1 min-w-0">
            <div class="flex items-center space-x-3 mb-2">
              <h1 class="text-2xl font-bold text-gray-900 truncate">
                Edit <%= @ai_provider_configuration.provider_name.titleize %> Configuration
              </h1>
              <!-- Status Indicator -->
              <div class="flex items-center space-x-2">
                <% if @ai_provider_configuration.provider_available? %>
                  <div class="relative">
                    <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                    <div class="absolute inset-0 w-3 h-3 bg-green-400 rounded-full animate-ping opacity-75"></div>
                  </div>
                  <span class="text-sm font-medium text-green-700">Available</span>
                <% else %>
                  <div class="w-3 h-3 bg-red-400 rounded-full"></div>
                  <span class="text-sm font-medium text-red-700">Not Configured</span>
                <% end %>
              </div>
            </div>

            <div class="flex flex-wrap items-center gap-3 mb-3">
              <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                <%= @ai_provider_configuration.ai_model_name %>
              </span>

              <% if @ai_provider_configuration.is_active? %>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                  <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-green-400" fill="currentColor" viewBox="0 0 8 8">
                    <circle cx="4" cy="4" r="3"/>
                  </svg>
                  Active
                </span>
              <% else %>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                  <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-gray-400" fill="currentColor" viewBox="0 0 8 8">
                    <circle cx="4" cy="4" r="3"/>
                  </svg>
                  Inactive
                </span>
              <% end %>
            </div>

            <p class="text-gray-600 text-sm">
              Configure advanced AI model settings, API credentials, and performance parameters
            </p>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="flex flex-col sm:flex-row gap-3 lg:flex-shrink-0">
          <%= button_to test_connection_admin_ai_provider_configuration_path(@ai_provider_configuration),
              method: :post,
              data: {
                action: "click->ai-provider-configuration-form#handleTestClick",
                ai_provider_configuration_form_target: "testButton"
              },
              class: "inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200" do %>
            <svg class="-ml-1 mr-2 h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
            </svg>
            Test Configuration
          <% end %>

          <%= link_to admin_ai_provider_configuration_path(@ai_provider_configuration),
              class: "inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200" do %>
            <svg class="-ml-1 mr-2 h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
            Cancel
          <% end %>
        </div>
      </div>
    </div>

    <!-- Configuration Form -->
    <%= form_with model: [:admin, @ai_provider_configuration],
        local: true,
        data: {
          controller: "ai-provider-configuration-form",
          ai_provider_configuration_form_provider_value: @ai_provider_configuration.provider_name,
          ai_provider_configuration_form_model_value: @ai_provider_configuration.ai_model_name
        },
        class: "space-y-8" do |form| %>

      <!-- Error Messages -->
      <% if @ai_provider_configuration.errors.any? %>
        <div class="bg-white rounded-xl shadow-sm p-6">
          <div class="rounded-lg bg-red-50 p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  Please correct the following <%= pluralize(@ai_provider_configuration.errors.count, "error") %>:
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul role="list" class="list-disc list-inside space-y-1">
                    <% @ai_provider_configuration.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <!-- Provider & Model Configuration -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900">Provider & Model Settings</h3>
            <p class="text-sm text-gray-600 mt-1">Configure the AI provider and model specifications</p>
          </div>
          <div class="flex items-center space-x-2">
            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
            </svg>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Provider Selection -->
          <div class="space-y-2">
            <%= form.label :provider_name, class: "block text-sm font-medium text-gray-700" do %>
              <span class="flex items-center">
                AI Provider
                <span class="text-red-500 ml-1">*</span>
                <button type="button"
                        class="ml-2 text-gray-400 hover:text-gray-600"
                        data-action="click->ai-provider-configuration-form#showTooltip"
                        data-tooltip="Select the AI service provider (OpenAI, Anthropic, etc.)">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                  </svg>
                </button>
              </span>
            <% end %>
            <%= form.select :provider_name,
                options_for_select([
                  ['OpenAI', 'openai'],
                  ['Anthropic', 'anthropic'],
                  ['Google Gemini', 'gemini'],
                  ['DeepSeek', 'deepseek'],
                  ['OpenRouter', 'openrouter']
                ], @ai_provider_configuration.provider_name),
                { prompt: "Select a provider..." },
                {
                  class: "mt-1 block w-full pl-3 pr-10 py-3 text-base border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-lg transition-colors duration-200",
                  data: {
                    action: "change->ai-provider-configuration-form#providerChanged",
                    ai_provider_configuration_form_target: "providerSelect"
                  }
                } %>
            <p class="text-xs text-gray-500 mt-1">Choose your preferred AI service provider</p>
          </div>

          <!-- Model Name -->
          <div class="space-y-2">
            <%= form.label :ai_model_name, class: "block text-sm font-medium text-gray-700" do %>
              <span class="flex items-center">
                Model Name
                <span class="text-red-500 ml-1">*</span>
                <button type="button"
                        class="ml-2 text-gray-400 hover:text-gray-600"
                        data-action="click->ai-provider-configuration-form#showTooltip"
                        data-tooltip="Specific model version (e.g., gpt-4o, claude-3-sonnet)">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                  </svg>
                </button>
              </span>
            <% end %>
            <%= form.text_field :ai_model_name,
                placeholder: "e.g., gpt-4o, claude-3-sonnet",
                class: "mt-1 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-lg py-3 px-4 transition-colors duration-200",
                data: {
                  action: "input->ai-provider-configuration-form#modelChanged",
                  ai_provider_configuration_form_target: "modelInput"
                } %>
            <p class="text-xs text-gray-500 mt-1">Enter the specific model identifier</p>
          </div>
        </div>
      </div>

      <!-- API Credentials & Connection -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900">API Credentials & Connection</h3>
            <p class="text-sm text-gray-600 mt-1">Secure authentication and endpoint configuration</p>
          </div>
          <div class="flex items-center space-x-2">
            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
            </svg>
          </div>
        </div>

        <div class="space-y-6">
          <!-- API Key Information -->
          <div class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">
              <span class="flex items-center">
                API Key Management
                <button type="button"
                        class="ml-2 text-gray-400 hover:text-gray-600"
                        data-action="click->ai-provider-configuration-form#showTooltip"
                        data-tooltip="API keys are managed through Rails credentials for security">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                  </svg>
                </button>
              </span>
            </label>

            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                  <svg class="w-5 h-5 text-blue-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <div class="flex-1">
                  <h4 class="text-sm font-medium text-blue-900">Secure API Key Storage</h4>
                  <div class="mt-2 text-sm text-blue-800">
                    <p class="mb-2">API keys for <strong><%= @ai_provider_configuration.provider_name.titleize %></strong> are managed through Rails encrypted credentials for maximum security.</p>

                    <div class="space-y-1">
                      <p><strong>Current Status:</strong>
                        <% if @ai_provider_configuration.provider_available? %>
                          <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            API Key Configured
                          </span>
                        <% else %>
                          <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                            </svg>
                            API Key Not Found
                          </span>
                        <% end %>
                      </p>

                      <% unless @ai_provider_configuration.provider_available? %>
                        <p class="text-xs text-blue-700 mt-2">
                          <strong>To configure the API key:</strong><br>
                          1. Add <code><%= @ai_provider_configuration.provider_name.upcase %>_API_KEY</code> to your environment variables, or<br>
                          2. Add <code><%= @ai_provider_configuration.provider_name.downcase %>_api_key</code> to Rails credentials
                        </p>
                      <% end %>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Base URL -->
          <div class="space-y-2">
            <%= form.label :base_url, class: "block text-sm font-medium text-gray-700" do %>
              <span class="flex items-center">
                Base URL
                <span class="text-gray-400 text-xs ml-2">(Optional)</span>
                <button type="button"
                        class="ml-2 text-gray-400 hover:text-gray-600"
                        data-action="click->ai-provider-configuration-form#showTooltip"
                        data-tooltip="Custom API endpoint URL. Leave blank to use default provider endpoint.">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                  </svg>
                </button>
              </span>
            <% end %>
            <%= form.url_field :base_url,
                placeholder: "https://api.example.com/v1",
                class: "mt-1 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-lg py-3 px-4 transition-colors duration-200",
                data: {
                  action: "input->ai-provider-configuration-form#baseUrlChanged",
                  ai_provider_configuration_form_target: "baseUrlInput"
                } %>
            <div class="flex items-start space-x-2 mt-2">
              <svg class="w-4 h-4 text-gray-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
              </svg>
              <p class="text-xs text-gray-600">
                Leave blank to use the default provider endpoint. Custom URLs are useful for proxy setups or alternative endpoints.
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Model Parameters -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900">Model Parameters</h3>
            <p class="text-sm text-gray-600 mt-1">Configure model behavior and performance settings</p>
          </div>
          <div class="flex items-center space-x-2">
            <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"/>
            </svg>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Max Tokens -->
          <div class="space-y-2">
            <%= form.label :max_tokens, class: "block text-sm font-medium text-gray-700" do %>
              <span class="flex items-center">
                Max Tokens
                <span class="text-red-500 ml-1">*</span>
                <button type="button"
                        class="ml-2 text-gray-400 hover:text-gray-600"
                        data-action="click->ai-provider-configuration-form#showTooltip"
                        data-tooltip="Maximum number of tokens the model can generate in a single response">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                  </svg>
                </button>
              </span>
            <% end %>
            <%= form.number_field :max_tokens,
                min: 1, max: 32000, step: 1,
                placeholder: "4096",
                class: "mt-1 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-lg py-3 px-4 transition-colors duration-200",
                data: {
                  action: "input->ai-provider-configuration-form#maxTokensChanged",
                  ai_provider_configuration_form_target: "maxTokensInput"
                } %>
            <p class="text-xs text-gray-600">Typical range: 1,024 - 8,192 tokens</p>
          </div>

          <!-- Temperature -->
          <div class="space-y-2">
            <%= form.label :temperature, class: "block text-sm font-medium text-gray-700" do %>
              <span class="flex items-center">
                Temperature
                <span class="text-red-500 ml-1">*</span>
                <button type="button"
                        class="ml-2 text-gray-400 hover:text-gray-600"
                        data-action="click->ai-provider-configuration-form#showTooltip"
                        data-tooltip="Controls randomness: 0 = deterministic, 1 = balanced, 2 = very creative">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                  </svg>
                </button>
              </span>
            <% end %>
            <div class="space-y-3">
              <%= form.number_field :temperature,
                  step: 0.1, min: 0, max: 2,
                  placeholder: "0.7",
                  class: "mt-1 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-lg py-3 px-4 transition-colors duration-200",
                  data: {
                    action: "input->ai-provider-configuration-form#temperatureChanged",
                    ai_provider_configuration_form_target: "temperatureInput"
                  } %>
              <!-- Temperature Visualization -->
              <div class="flex items-center space-x-3">
                <span class="text-xs text-gray-500 w-16">Precise</span>
                <div class="flex-1 bg-gray-200 rounded-full h-2">
                  <div class="bg-gradient-to-r from-blue-500 via-green-500 to-red-500 h-2 rounded-full"
                       style="width: <%= (@ai_provider_configuration.temperature * 50).round(1) %>%"
                       data-ai-provider-configuration-form-target="temperatureBar"></div>
                </div>
                <span class="text-xs text-gray-500 w-16">Creative</span>
              </div>
            </div>
            <p class="text-xs text-gray-600">0.0 = deterministic, 1.0 = balanced, 2.0 = very creative</p>
          </div>

          <!-- Cost per Token -->
          <div class="space-y-2">
            <%= form.label :cost_per_token, class: "block text-sm font-medium text-gray-700" do %>
              <span class="flex items-center">
                Cost per Token (USD)
                <span class="text-red-500 ml-1">*</span>
                <button type="button"
                        class="ml-2 text-gray-400 hover:text-gray-600"
                        data-action="click->ai-provider-configuration-form#showTooltip"
                        data-tooltip="Cost in USD per token for usage tracking and billing">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                  </svg>
                </button>
              </span>
            <% end %>
            <%= form.number_field :cost_per_token,
                step: 0.000001, min: 0,
                placeholder: "0.000015",
                class: "mt-1 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-lg py-3 px-4 transition-colors duration-200",
                data: {
                  action: "input->ai-provider-configuration-form#costChanged",
                  ai_provider_configuration_form_target: "costInput"
                } %>
            <p class="text-xs text-gray-600">Used for cost tracking and budget management</p>
          </div>

          <!-- Priority -->
          <div class="space-y-2">
            <%= form.label :priority, class: "block text-sm font-medium text-gray-700" do %>
              <span class="flex items-center">
                Priority Level
                <span class="text-red-500 ml-1">*</span>
                <button type="button"
                        class="ml-2 text-gray-400 hover:text-gray-600"
                        data-action="click->ai-provider-configuration-form#showTooltip"
                        data-tooltip="Priority for model selection: 1 = highest priority, 10 = lowest priority">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                  </svg>
                </button>
              </span>
            <% end %>
            <div class="space-y-3">
              <%= form.number_field :priority,
                  min: 1, max: 10, step: 1,
                  placeholder: "5",
                  class: "mt-1 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-lg py-3 px-4 transition-colors duration-200",
                  data: {
                    action: "input->ai-provider-configuration-form#priorityChanged",
                    ai_provider_configuration_form_target: "priorityInput"
                  } %>
              <!-- Priority Visualization -->
              <div class="flex items-center space-x-3">
                <span class="text-xs text-gray-500 w-16">Highest</span>
                <div class="flex-1 bg-gray-200 rounded-full h-2">
                  <div class="bg-gradient-to-r from-green-500 to-red-500 h-2 rounded-full"
                       style="width: <%= (11 - (@ai_provider_configuration.priority || 5)) * 10 %>%"
                       data-ai-provider-configuration-form-target="priorityBar"></div>
                </div>
                <span class="text-xs text-gray-500 w-16">Lowest</span>
              </div>
            </div>
            <p class="text-xs text-gray-600">Lower numbers = higher priority for model selection</p>
          </div>
        </div>
      </div>

      <!-- Advanced Settings -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900">Advanced Settings</h3>
            <p class="text-sm text-gray-600 mt-1">Task assignments, activation status, and specialized configuration</p>
          </div>
          <div class="flex items-center space-x-2">
            <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
            </svg>
          </div>
        </div>

        <div class="space-y-6">
          <!-- Activation Status -->
          <div class="space-y-4">
            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                    </svg>
                  </div>
                </div>
                <div>
                  <h4 class="text-sm font-medium text-gray-900">Configuration Status</h4>
                  <p class="text-sm text-gray-600">Enable or disable this AI provider configuration</p>
                </div>
              </div>
              <div class="flex items-center">
                <%= form.check_box :is_active,
                    class: "focus:ring-2 focus:ring-blue-500 h-5 w-5 text-blue-600 border-gray-300 rounded transition-colors duration-200",
                    data: {
                      action: "change->ai-provider-configuration-form#activeStatusChanged",
                      ai_provider_configuration_form_target: "activeCheckbox"
                    } %>
                <%= form.label :is_active, "Active", class: "ml-3 text-sm font-medium text-gray-700" %>
              </div>
            </div>
            <p class="text-xs text-gray-600 flex items-center">
              <svg class="w-4 h-4 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
              </svg>
              Only active configurations will be used for AI requests and model selection.
            </p>
          </div>

          <!-- Supported Task Types -->
          <div class="space-y-4">
            <%= form.label :task_types, "Supported Task Types", class: "block text-sm font-medium text-gray-700" do %>
              <span class="flex items-center">
                Supported Task Types
                <button type="button"
                        class="ml-2 text-gray-400 hover:text-gray-600"
                        data-action="click->ai-provider-configuration-form#showTooltip"
                        data-tooltip="Select which types of AI tasks this configuration should handle">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                  </svg>
                </button>
              </span>
            <% end %>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 mt-3">
              <% [
                ['email_generation', 'Email Generation', 'Generate marketing emails and newsletters'],
                ['social_content', 'Social Content', 'Create social media posts and content'],
                ['blog_writing', 'Blog Writing', 'Write blog posts and articles'],
                ['seo_optimization', 'SEO Optimization', 'Optimize content for search engines'],
                ['campaign_analysis', 'Campaign Analysis', 'Analyze marketing campaign performance'],
                ['creative_content', 'Creative Content', 'Generate creative marketing materials']
              ].each do |task_type, label, description| %>
                <div class="relative">
                  <div class="flex items-start p-3 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors duration-200 <%= @ai_provider_configuration.task_types.include?(task_type) ? 'bg-blue-50 border-blue-300' : 'bg-white' %>">
                    <div class="flex items-center h-5">
                      <%= check_box_tag "ai_provider_configuration[task_types][]", task_type,
                          @ai_provider_configuration.task_types.include?(task_type),
                          id: "task_type_#{task_type}",
                          class: "focus:ring-2 focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded transition-colors duration-200" %>
                    </div>
                    <div class="ml-3 text-sm">
                      <%= label_tag "task_type_#{task_type}", label,
                          class: "font-medium text-gray-900 cursor-pointer" %>
                      <p class="text-gray-500 text-xs mt-1"><%= description %></p>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>

            <p class="text-xs text-gray-600 flex items-center mt-3">
              <svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              Select the types of AI tasks this configuration should handle. Leave empty to use for all task types.
            </p>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div>
            <h3 class="text-lg font-semibold text-gray-900">Save Configuration</h3>
            <p class="text-sm text-gray-600 mt-1">Review your settings and save the configuration</p>
          </div>

          <div class="flex flex-col sm:flex-row gap-3">
            <!-- Test Configuration Button -->
            <%= button_to test_connection_admin_ai_provider_configuration_path(@ai_provider_configuration),
                method: :post,
                data: {
                  action: "click->ai-provider-configuration-form#handleTestClick",
                  ai_provider_configuration_form_target: "testButton2"
                },
                class: "inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200" do %>
              <svg class="-ml-1 mr-2 h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
              </svg>
              Test Configuration
            <% end %>

            <!-- Cancel Button -->
            <%= link_to admin_ai_provider_configuration_path(@ai_provider_configuration),
                class: "inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200" do %>
              <svg class="-ml-1 mr-2 h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
              Cancel
            <% end %>

            <!-- Save Button -->
            <%= form.submit "Update Configuration",
                class: "inline-flex items-center justify-center px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200",
                data: {
                  action: "click->ai-provider-configuration-form#saveConfiguration",
                  ai_provider_configuration_form_target: "saveButton"
                } %>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Danger Zone -->
    <div class="bg-white rounded-xl shadow-sm border border-red-200 p-6">
      <div class="flex items-start space-x-4">
        <div class="flex-shrink-0">
          <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
            </svg>
          </div>
        </div>
        <div class="flex-1 min-w-0">
          <h3 class="text-lg font-semibold text-gray-900">Danger Zone</h3>
          <div class="mt-2 text-sm text-gray-600">
            <p>Permanently delete this AI provider configuration. This action cannot be undone and will remove all associated data.</p>
          </div>
          <div class="mt-4 flex flex-col sm:flex-row gap-3">
            <%= button_to admin_ai_provider_configuration_path(@ai_provider_configuration),
                method: :delete,
                data: {
                  turbo_confirm: "Are you sure you want to delete this configuration?\n\nThis will permanently remove:\n• All configuration settings\n• Usage history and statistics\n• Any associated data\n\nThis action cannot be undone.\n\nType 'DELETE' to confirm:",
                  turbo_method: :delete
                },
                class: "inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-red-700 bg-red-100 border border-red-300 rounded-lg hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200" do %>
              <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
              </svg>
              Delete Configuration
            <% end %>

            <div class="text-xs text-gray-500 flex items-center">
              <svg class="w-4 h-4 text-gray-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
              </svg>
              This action is irreversible and will permanently remove all data
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Configuration Preview Modal (Hidden by default) -->
<div id="configuration-preview-modal"
     class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
     data-ai-provider-configuration-form-target="previewModal">
  <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-xl bg-white">
    <div class="mt-3">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">Configuration Preview</h3>
        <button type="button"
                class="text-gray-400 hover:text-gray-600"
                data-action="click->ai-provider-configuration-form#closePreview">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>

      <div class="space-y-4" data-ai-provider-configuration-form-target="previewContent">
        <!-- Preview content will be populated by Stimulus controller -->
      </div>

      <div class="flex justify-end space-x-3 mt-6">
        <button type="button"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
                data-action="click->ai-provider-configuration-form#closePreview">
          Close
        </button>
        <button type="button"
                class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700"
                data-action="click->ai-provider-configuration-form#testFromPreview">
          Test Configuration
        </button>
      </div>
    </div>
  </div>
</div>
