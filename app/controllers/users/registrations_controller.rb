# frozen_string_literal: true

class Users::RegistrationsController < Devise::RegistrationsController
  before_action :configure_sign_up_params, only: [:create]
  before_action :configure_account_update_params, only: [:update]

  # GET /resource/sign_up
  def new
    super
  end

  # POST /resource
  def create
    build_resource(sign_up_params)

    # Create tenant for the new user first, then assign it before validation
    begin
      tenant = create_tenant_for_user
      if tenant.persisted?
        resource.tenant = tenant
        ActsAsTenant.current_tenant = tenant

        # Now validate the resource with the tenant assigned
        if resource.valid?
          resource.save
          yield resource if block_given?

          if resource.persisted?
            if resource.active_for_authentication?
              set_flash_message! :notice, :signed_up
              sign_up(resource_name, resource)
              respond_with resource, location: after_sign_up_path_for(resource)
            else
              set_flash_message! :notice, :"signed_up_but_#{resource.inactive_message}"
              expire_data_after_sign_up!
              respond_with resource, location: after_inactive_sign_up_path_for(resource)
            end
          else
            clean_up_passwords resource
            set_minimum_password_length
            respond_with resource
          end
        else
          # User validation failed after tenant assignment
          clean_up_passwords resource
          set_minimum_password_length
          respond_with resource
        end
      else
        # Tenant creation failed
        Rails.logger.error "Tenant creation failed: #{tenant.errors.full_messages}"
        tenant.errors.full_messages.each do |error|
          resource.errors.add(:base, "Account setup failed: #{error}")
        end
        clean_up_passwords resource
        set_minimum_password_length
        respond_with resource
      end
    rescue => e
      Rails.logger.error "Registration error: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      resource.errors.add(:base, "Registration failed. Please try again.")
      clean_up_passwords resource
      set_minimum_password_length
      respond_with resource
    end
  end

  # GET /resource/edit
  def edit
    super
  end

  # PUT /resource
  def update
    super
  end

  # DELETE /resource
  def destroy
    super
  end

  # GET /resource/cancel
  def cancel
    super
  end

  protected

  # If you have extra params to permit, append them to the sanitizer.
  def configure_sign_up_params
    devise_parameter_sanitizer.permit(:sign_up, keys: [:first_name, :last_name])
  end

  # If you have extra params to permit, append them to the sanitizer.
  def configure_account_update_params
    devise_parameter_sanitizer.permit(:account_update, keys: [:first_name, :last_name])
  end

  # The path used after sign up.
  def after_sign_up_path_for(resource)
    dashboard_path
  end

  # The path used after sign up for inactive accounts.
  def after_inactive_sign_up_path_for(resource)
    new_user_session_path
  end

  private

  def create_tenant_for_user
    # Generate a unique subdomain based on user's name and email
    base_subdomain = generate_subdomain
    subdomain = ensure_unique_subdomain(base_subdomain)
    
    # Create tenant name from user's full name or email
    tenant_name = "#{resource.first_name} #{resource.last_name}".strip
    tenant_name = resource.email.split('@').first.titleize if tenant_name.blank?
    
    Tenant.create(
      name: tenant_name,
      subdomain: subdomain,
      status: 'active'
    )
  end

  def generate_subdomain
    # Try to create a subdomain from first name + last name
    if resource.first_name.present? && resource.last_name.present?
      base = "#{resource.first_name}#{resource.last_name}".downcase.gsub(/[^a-z0-9]/, '')
    else
      # Fallback to email username
      base = resource.email.split('@').first.downcase.gsub(/[^a-z0-9]/, '')
    end
    
    # Ensure minimum length
    base = "user#{base}" if base.length < 3
    
    # Truncate if too long
    base[0..20]
  end

  def ensure_unique_subdomain(base_subdomain)
    subdomain = base_subdomain
    counter = 1
    
    while Tenant.exists?(subdomain: subdomain)
      subdomain = "#{base_subdomain}#{counter}"
      counter += 1
    end
    
    subdomain
  end
end
