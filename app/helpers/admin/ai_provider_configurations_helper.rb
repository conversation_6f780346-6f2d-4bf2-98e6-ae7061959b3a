module Admin::AiProviderConfigurationsHelper
  # Provider status indicator with visual styling
  def provider_status_indicator(configuration)
    if configuration.provider_available?
      content_tag :div, class: "flex items-center space-x-2" do
        content_tag(:div, "", class: "relative") do
          content_tag(:div, "", class: "w-3 h-3 bg-green-400 rounded-full") +
          content_tag(:div, "", class: "absolute inset-0 w-3 h-3 bg-green-400 rounded-full animate-ping opacity-75")
        end +
        content_tag(:span, "Available", class: "text-sm font-medium text-green-700")
      end
    else
      content_tag :div, class: "flex items-center space-x-2" do
        content_tag(:div, "", class: "w-3 h-3 bg-red-400 rounded-full") +
        content_tag(:span, "Not Configured", class: "text-sm font-medium text-red-700")
      end
    end
  end

  # Provider icon based on provider name
  def provider_icon(provider_name)
    case provider_name.downcase
    when 'openai'
      # OpenAI icon SVG
      content_tag :svg, class: "w-8 h-8 text-blue-600", fill: "currentColor", viewBox: "0 0 24 24" do
        content_tag :path, "", d: "M22.2819 9.8211a5.9847 5.9847 0 0 0-.5157-4.9108 6.0462 6.0462 0 0 0-6.5098-2.9A6.0651 6.0651 0 0 0 4.9807 4.1818a5.9847 5.9847 0 0 0-3.9977 2.9 6.0462 6.0462 0 0 0 .7427 7.0966 5.98 5.98 0 0 0 .511 4.9107 6.051 6.051 0 0 0 6.5146 2.9001A5.9847 5.9847 0 0 0 13.2599 24a6.0557 6.0557 0 0 0 5.7718-4.2058 5.9894 5.9894 0 0 0 3.9977-2.9001 6.0557 6.0557 0 0 0-.7475-7.0729zm-9.022 12.6081a4.4755 4.4755 0 0 1-2.8764-1.0408l.1419-.0804 4.7783-2.7582a.7948.7948 0 0 0 .3927-.6813v-6.7369l2.02 1.1686a.071.071 0 0 1 .038.052v5.5826a4.504 4.504 0 0 1-4.4945 4.4944zm-9.6607-4.1254a4.4708 4.4708 0 0 1-.5346-3.0137l.142.0852 4.783 2.7582a.7712.7712 0 0 0 .7806 0l5.8428-3.3685v2.3324a.0804.0804 0 0 1-.0332.0615L9.74 19.9502a4.4992 4.4992 0 0 1-6.1408-1.6464zM2.3408 7.8956a4.485 4.485 0 0 1 2.3655-1.9728V11.6a.7664.7664 0 0 0 .3879.6765l5.8144 3.3543-2.0201 1.1685a.0757.0757 0 0 1-.071 0l-4.8303-2.7865A4.504 4.504 0 0 1 2.3408 7.872zm16.5963 3.8558L13.1038 8.364 15.1192 7.2a.0757.0757 0 0 1 .071 0l4.8303 2.7913a4.4944 4.4944 0 0 1-.6765 8.1042v-5.6772a.79.79 0 0 0-.407-.667zm2.0107-3.0231l-.142-.0852-4.7735-2.7818a.7759.7759 0 0 0-.7854 0L9.409 9.2297V6.8974a.0662.0662 0 0 1 .0284-.0615l4.8303-2.7866a4.4992 4.4992 0 0 1 6.6802 4.66zM8.3065 12.863l-2.02-1.1638a.0804.0804 0 0 1-.038-.0567V6.0742a4.4992 4.4992 0 0 1 7.3757-3.4537l-.142.0805L8.704 5.459a.7948.7948 0 0 0-.3927.6813zm1.0976-2.3654l2.602-1.4998 2.6069 1.4998v2.9994l-2.5974 1.4997-2.6067-1.4997Z"
      end
    when 'anthropic'
      # Anthropic icon SVG
      content_tag :svg, class: "w-8 h-8 text-orange-600", fill: "currentColor", viewBox: "0 0 24 24" do
        content_tag :path, "", d: "M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"
      end
    else
      # Default AI icon
      content_tag :svg, class: "w-8 h-8 text-gray-600", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" do
        content_tag :path, "", "stroke-linecap": "round", "stroke-linejoin": "round", "stroke-width": "2", d: "M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
      end
    end
  end

  # Format cost with appropriate currency symbol and precision
  def format_cost(cost)
    if cost < 0.001
      "$#{sprintf('%.6f', cost)}"
    elsif cost < 0.01
      "$#{sprintf('%.4f', cost)}"
    else
      "$#{sprintf('%.2f', cost)}"
    end
  end

  # Temperature visualization helper
  def temperature_percentage(temperature)
    (temperature * 100).round(1)
  end

  # Priority level description
  def priority_description(priority)
    case priority
    when 1..2
      "Highest Priority"
    when 3..4
      "High Priority"
    when 5..6
      "Medium Priority"
    when 7..8
      "Low Priority"
    else
      "Lowest Priority"
    end
  end

  # Health status color class
  def health_status_class(configuration)
    if configuration.provider_available? && configuration.is_active?
      "text-green-600"
    elsif configuration.provider_available?
      "text-yellow-600"
    else
      "text-red-600"
    end
  end

  # Usage trend indicator (placeholder for future implementation)
  def usage_trend_indicator(current_usage, previous_usage)
    return "text-gray-500" if previous_usage.zero?

    percentage_change = ((current_usage - previous_usage) / previous_usage.to_f * 100).round(1)

    if percentage_change > 0
      "text-green-600"
    elsif percentage_change < 0
      "text-red-600"
    else
      "text-gray-500"
    end
  end

  # Form field validation state classes
  def field_validation_class(object, field)
    if object.errors[field].any?
      "border-red-300 focus:border-red-500 focus:ring-red-500"
    else
      "border-gray-300 focus:border-blue-500 focus:ring-blue-500"
    end
  end

  # Required field indicator
  def required_field_indicator
    content_tag :span, "*", class: "text-red-500 ml-1"
  end

  # Provider model suggestions
  def provider_model_suggestions(provider_name)
    suggestions = {
      'openai' => ['gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo', 'gpt-3.5-turbo'],
      'anthropic' => ['claude-3-5-sonnet-20241022', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307'],
      'gemini' => ['gemini-1.5-pro', 'gemini-1.5-flash', 'gemini-pro'],
      'deepseek' => ['deepseek-chat', 'deepseek-coder'],
      'openrouter' => ['openai/gpt-4o', 'anthropic/claude-3-sonnet', 'google/gemini-pro']
    }

    suggestions[provider_name.downcase] || []
  end

  # Default cost per token for models
  def default_cost_for_model(model_name)
    costs = {
      'gpt-4o' => 0.000015,
      'gpt-4o-mini' => 0.000001,
      'gpt-4-turbo' => 0.00003,
      'gpt-3.5-turbo' => 0.000002,
      'claude-3-5-sonnet' => 0.000015,
      'claude-3-sonnet' => 0.000015,
      'claude-3-haiku' => 0.000001,
      'gemini-1.5-pro' => 0.000007,
      'gemini-1.5-flash' => 0.000001,
      'deepseek-chat' => 0.000001,
      'deepseek-coder' => 0.000001
    }

    model_key = costs.keys.find { |key| model_name.downcase.include?(key) }
    costs[model_key] || 0.000010
  end

  # Configuration completeness indicator
  def configuration_completeness(configuration)
    required_fields = [:provider_name, :ai_model_name, :cost_per_token, :max_tokens, :temperature, :priority]
    completed_fields = required_fields.count { |field| configuration.send(field).present? }
    percentage = (completed_fields.to_f / required_fields.length * 100).round

    {
      percentage: percentage,
      completed: completed_fields,
      total: required_fields.length,
      status: percentage == 100 ? 'complete' : percentage >= 80 ? 'mostly_complete' : 'incomplete'
    }
  end

  # Configuration status badge
  def configuration_status_badge(configuration)
    completeness = configuration_completeness(configuration)

    case completeness[:status]
    when 'complete'
      content_tag :span, "Complete", class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
    when 'mostly_complete'
      content_tag :span, "#{completeness[:percentage]}% Complete", class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
    else
      content_tag :span, "Incomplete", class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"
    end
  end

  # Task type descriptions
  def task_type_description(task_type)
    descriptions = {
      'email_generation' => 'Generate marketing emails and newsletters',
      'social_content' => 'Create social media posts and content',
      'blog_writing' => 'Write blog posts and articles',
      'seo_optimization' => 'Optimize content for search engines',
      'campaign_analysis' => 'Analyze marketing campaign performance',
      'creative_content' => 'Generate creative marketing materials'
    }

    descriptions[task_type] || 'AI-powered content generation'
  end

  # Form section icons
  def form_section_icon(section_type)
    icons = {
      'provider' => 'M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z',
      'credentials' => 'M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z',
      'parameters' => 'M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4',
      'advanced' => 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z'
    }

    path = icons[section_type] || icons['provider']

    content_tag :svg, class: "w-5 h-5", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" do
      content_tag :path, "", "stroke-linecap": "round", "stroke-linejoin": "round", "stroke-width": "2", d: path
    end
  end
end
