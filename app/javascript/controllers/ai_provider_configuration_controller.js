import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="ai-provider-configuration"
export default class extends Controller {
  static targets = ["statusIndicator", "connectionTest", "toggleButton"]
  static values = { 
    providerId: String,
    isActive: <PERSON>olean,
    isAvailable: <PERSON><PERSON><PERSON>
  }

  connect() {
    console.log("AI Provider Configuration controller connected")
    this.updateStatusIndicator()
  }

  // Update the visual status indicator
  updateStatusIndicator() {
    if (this.hasStatusIndicatorTarget) {
      const indicator = this.statusIndicatorTarget
      
      if (this.isAvailableValue && this.isActiveValue) {
        indicator.className = "w-3 h-3 bg-green-400 rounded-full animate-pulse"
      } else if (this.isAvailableValue) {
        indicator.className = "w-3 h-3 bg-yellow-400 rounded-full"
      } else {
        indicator.className = "w-3 h-3 bg-red-400 rounded-full"
      }
    }
  }

  // Handle connection test with loading state
  testConnection(event) {
    event.preventDefault()
    
    if (this.hasConnectionTestTarget) {
      const button = this.connectionTestTarget
      const originalText = button.textContent
      
      // Show loading state
      button.textContent = "Testing..."
      button.disabled = true
      button.classList.add("opacity-50", "cursor-not-allowed")
      
      // Submit the form
      const form = event.target.closest('form') || event.target
      if (form.tagName === 'A') {
        // Handle link-based submission
        window.location.href = form.href
      } else {
        form.submit()
      }
      
      // Reset button state after a delay (in case of errors)
      setTimeout(() => {
        button.textContent = originalText
        button.disabled = false
        button.classList.remove("opacity-50", "cursor-not-allowed")
      }, 5000)
    }
  }

  // Handle toggle with confirmation and loading state
  toggleStatus(event) {
    const action = this.isActiveValue ? "deactivate" : "activate"
    const confirmation = `Are you sure you want to ${action} this AI provider?`
    
    if (!confirm(confirmation)) {
      event.preventDefault()
      return false
    }
    
    if (this.hasToggleButtonTarget) {
      const button = this.toggleButtonTarget
      const originalText = button.textContent
      
      // Show loading state
      button.textContent = this.isActiveValue ? "Deactivating..." : "Activating..."
      button.disabled = true
      button.classList.add("opacity-50", "cursor-not-allowed")
      
      // The form will submit normally via Turbo
    }
  }

  // Handle successful status updates (called via Turbo events)
  statusUpdated(event) {
    const { isActive, isAvailable } = event.detail
    this.isActiveValue = isActive
    this.isAvailableValue = isAvailable
    this.updateStatusIndicator()
    
    // Show success feedback
    this.showFeedback("Status updated successfully", "success")
  }

  // Handle connection test results
  connectionTested(event) {
    const { success, responseTime, error } = event.detail
    
    if (success) {
      this.showFeedback(`Connection successful! Response time: ${responseTime}ms`, "success")
      this.isAvailableValue = true
    } else {
      this.showFeedback(`Connection failed: ${error}`, "error")
      this.isAvailableValue = false
    }
    
    this.updateStatusIndicator()
  }

  // Show temporary feedback messages
  showFeedback(message, type = "info") {
    // Create feedback element
    const feedback = document.createElement("div")
    feedback.className = `fixed top-4 right-4 px-4 py-2 rounded-lg shadow-lg z-50 transition-all duration-300 ${
      type === "success" ? "bg-green-100 text-green-800 border border-green-200" :
      type === "error" ? "bg-red-100 text-red-800 border border-red-200" :
      "bg-blue-100 text-blue-800 border border-blue-200"
    }`
    feedback.textContent = message
    
    // Add to page
    document.body.appendChild(feedback)
    
    // Animate in
    setTimeout(() => {
      feedback.classList.add("translate-y-0", "opacity-100")
    }, 10)
    
    // Remove after delay
    setTimeout(() => {
      feedback.classList.add("translate-y-2", "opacity-0")
      setTimeout(() => {
        if (feedback.parentNode) {
          feedback.parentNode.removeChild(feedback)
        }
      }, 300)
    }, 3000)
  }

  // Refresh provider status (for future real-time updates)
  refreshStatus() {
    fetch(`/admin/ai_provider_configurations/${this.providerIdValue}/status`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    })
    .then(response => response.json())
    .then(data => {
      this.isActiveValue = data.is_active
      this.isAvailableValue = data.is_available
      this.updateStatusIndicator()
    })
    .catch(error => {
      console.error('Error refreshing status:', error)
    })
  }
}
