require 'rails_helper'

RSpec.describe Users::RegistrationsController, type: :controller do
  before do
    @request.env["devise.mapping"] = Devise.mappings[:user]
  end

  describe "POST #create" do
    let(:valid_attributes) do
      {
        first_name: "<PERSON>",
        last_name: "<PERSON><PERSON>",
        email: "<EMAIL>",
        password: "password123",
        password_confirmation: "password123"
      }
    end

    context "with valid parameters" do
      it "creates a new user and tenant" do
        expect {
          post :create, params: {
            user: valid_attributes,
            terms: "on"
          }
        }.to change(User, :count).by(1).and change(Tenant, :count).by(1)

        expect(response).to redirect_to("/dashboard")
        
        user = User.find_by(email: valid_attributes[:email])
        expect(user).to be_present
        expect(user.first_name).to eq(valid_attributes[:first_name])
        expect(user.last_name).to eq(valid_attributes[:last_name])
        expect(user.role).to eq("owner")
        
        expect(user.tenant).to be_present
        expect(user.tenant.name).to eq("#{valid_attributes[:first_name]} #{valid_attributes[:last_name]}")
        expect(user.tenant.status).to eq("active")
      end

      it "generates unique subdomains for users with same names" do
        # Create first user
        post :create, params: {
          user: valid_attributes,
          terms: "on"
        }
        
        first_user = User.find_by(email: valid_attributes[:email])
        first_subdomain = first_user.tenant.subdomain

        # Create second user with same name but different email
        second_attributes = valid_attributes.merge(email: "<EMAIL>")
        
        expect {
          post :create, params: {
            user: second_attributes,
            terms: "on"
          }
        }.to change(User, :count).by(1).and change(Tenant, :count).by(1)

        second_user = User.find_by(email: second_attributes[:email])
        second_subdomain = second_user.tenant.subdomain

        expect(first_subdomain).not_to eq(second_subdomain)
        expect(second_subdomain).to match(/johndoe\d+/)
      end
    end

    context "with invalid parameters" do
      it "does not create user with missing required fields" do
        expect {
          post :create, params: {
            user: {
              first_name: "",
              last_name: "",
              email: "",
              password: "",
              password_confirmation: ""
            }
          }
        }.not_to change(User, :count)

        expect(response).to render_template(:new)
      end

      it "does not create user with mismatched passwords" do
        invalid_attributes = valid_attributes.merge(
          password_confirmation: "different_password"
        )

        expect {
          post :create, params: {
            user: invalid_attributes,
            terms: "on"
          }
        }.not_to change(User, :count)

        expect(response).to render_template(:new)
      end

      it "does not create user with duplicate email" do
        # Create first user
        existing_tenant = create(:tenant)
        create(:user, email: valid_attributes[:email], tenant: existing_tenant)

        expect {
          post :create, params: {
            user: valid_attributes,
            terms: "on"
          }
        }.not_to change(User, :count)

        expect(response).to render_template(:new)
      end
    end
  end
end
