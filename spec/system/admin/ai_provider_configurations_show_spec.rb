require 'rails_helper'

RSpec.describe "Admin AI Provider Configuration Show Page", type: :system do
  let(:tenant) { create(:tenant) }
  let(:admin_user) { create(:user, :admin, tenant: tenant) }
  let(:ai_provider_configuration) { create(:ai_provider_configuration, tenant: tenant, provider_name: "openai", ai_model_name: "gpt-4o-mini") }

  before do
    ActsAsTenant.current_tenant = tenant
    sign_in admin_user
  end

  describe "page layout and design" do
    it "displays the modern redesigned layout" do
      visit admin_ai_provider_configuration_path(ai_provider_configuration)

      # Check for modern header section
      expect(page).to have_content(ai_provider_configuration.provider_name.titleize)
      expect(page).to have_content(ai_provider_configuration.ai_model_name)
      
      # Check for status indicators
      expect(page).to have_css('.bg-green-400, .bg-red-400') # Status indicator dots
      
      # Check for action buttons
      expect(page).to have_button("Test Connection")
      expect(page).to have_link("Edit Configuration")
      
      # Check for main content sections
      expect(page).to have_content("Core Configuration")
      expect(page).to have_content("Advanced Settings")
      expect(page).to have_content("Quick Actions")
      expect(page).to have_content("Usage Analytics")
      expect(page).to have_content("Provider Health")
    end

    it "displays provider-specific information correctly" do
      visit admin_ai_provider_configuration_path(ai_provider_configuration)

      # Check configuration details
      expect(page).to have_content("Provider")
      expect(page).to have_content("Model Name")
      expect(page).to have_content("Base URL")
      expect(page).to have_content("Max Tokens")
      expect(page).to have_content("Temperature")
      expect(page).to have_content("Cost per Token")
      
      # Check values are displayed
      expect(page).to have_content(ai_provider_configuration.provider_name.titleize)
      expect(page).to have_content(ai_provider_configuration.ai_model_name)
      expect(page).to have_content(ai_provider_configuration.base_url)
    end

    it "shows usage statistics" do
      visit admin_ai_provider_configuration_path(ai_provider_configuration)

      expect(page).to have_content("Total Requests")
      expect(page).to have_content("Success Rate")
      expect(page).to have_content("Avg Response Time")
      expect(page).to have_content("Total Cost")
    end

    it "displays provider health information" do
      visit admin_ai_provider_configuration_path(ai_provider_configuration)

      expect(page).to have_content("Provider Health")
      expect(page).to have_content("API Connection")
      expect(page).to have_content("Configuration")
      expect(page).to have_content("Last Check")
    end
  end

  describe "interactive functionality" do
    it "has working test connection button" do
      visit admin_ai_provider_configuration_path(ai_provider_configuration)

      # Should have test connection buttons (both in header and sidebar)
      test_buttons = page.all("input[value='Test Connection'], button", text: "Test")
      expect(test_buttons.count).to be >= 1
    end

    it "has working toggle active button" do
      visit admin_ai_provider_configuration_path(ai_provider_configuration)

      if ai_provider_configuration.is_active?
        expect(page).to have_button("Deactivate")
      else
        expect(page).to have_button("Activate")
      end
    end

    it "displays correct status badges" do
      visit admin_ai_provider_configuration_path(ai_provider_configuration)

      if ai_provider_configuration.is_active?
        expect(page).to have_content("Active")
      else
        expect(page).to have_content("Inactive")
      end
    end
  end

  describe "responsive design" do
    it "works on mobile viewport", js: true do
      page.driver.browser.manage.window.resize_to(375, 667) # iPhone SE size
      visit admin_ai_provider_configuration_path(ai_provider_configuration)

      # Page should still be functional on mobile
      expect(page).to have_content(ai_provider_configuration.provider_name.titleize)
      expect(page).to have_button("Test Connection")
    end
  end

  describe "accessibility" do
    it "has proper heading structure" do
      visit admin_ai_provider_configuration_path(ai_provider_configuration)

      # Should have proper heading hierarchy
      expect(page).to have_css('h1')
      expect(page).to have_css('h3')
    end

    it "has proper form labels and buttons" do
      visit admin_ai_provider_configuration_path(ai_provider_configuration)

      # Buttons should have proper text content
      expect(page).to have_button("Test Connection")
      
      # Links should have descriptive text
      expect(page).to have_link("Edit Configuration")
    end
  end

  describe "breadcrumb navigation" do
    it "shows proper breadcrumb navigation" do
      visit admin_ai_provider_configuration_path(ai_provider_configuration)

      expect(page).to have_link("AI Provider Configurations")
      expect(page).to have_content(ai_provider_configuration.provider_name.titleize)
    end

    it "breadcrumb link works" do
      visit admin_ai_provider_configuration_path(ai_provider_configuration)

      click_link "AI Provider Configurations"
      expect(current_path).to eq(admin_ai_provider_configurations_path)
    end
  end
end
