#!/usr/bin/env ruby

# Manual test script to verify registration functionality
puts "🧪 Manual Registration Test"
puts "=" * 60

# Load Rails environment
require_relative 'config/environment'

# Try development environment first
Rails.env = 'development'

# Explicitly require Devise
require 'devise'

# Reload routes
Rails.application.reload_routes!

puts "\n🔍 Testing Routes Loading:"

# Try to load routes manually with error handling
begin
  puts "  📋 Loading routes manually..."

  # Clear existing routes
  Rails.application.routes.clear!

  # Load routes with error handling
  Rails.application.routes.draw do
    # Test if we can load just the Devise routes
    begin
      devise_for :users, controllers: {
        sessions: "users/sessions",
        registrations: "users/registrations"
      }
      puts "    ✅ Devise routes loaded successfully"
    rescue => e
      puts "    ❌ Devise routes error: #{e.message}"
      puts "    #{e.backtrace.first}"
    end

    # Test a simple route
    begin
      root "marketing#index"
      puts "    ✅ Root route loaded successfully"
    rescue => e
      puts "    ❌ Root route error: #{e.message}"
    end
  end

  puts "  📊 Routes loaded: #{Rails.application.routes.routes.count}"

  # Check Devise mappings
  puts "  📦 Devise mappings: #{Devise.mappings.keys}"

  # Test route recognition
  begin
    route_info = Rails.application.routes.recognize_path("/users", method: :post)
    puts "  ✅ Route found: #{route_info}"
  rescue => e
    puts "  ❌ Route error: #{e.message}"
  end

rescue => e
  puts "  ❌ Routes loading error: #{e.message}"
  puts "  #{e.backtrace.first}"
end

puts "\n🔧 Testing Registration Controller:"

# Test data
user_params = {
  first_name: 'John',
  last_name: 'Doe',
  email: '<EMAIL>',
  password: 'password123',
  password_confirmation: 'password123'
}

begin
  # Clean up any existing test data
  existing_user = User.find_by(email: user_params[:email])
  if existing_user
    puts "  🧹 Cleaning up existing test user"
    existing_user.tenant&.destroy
    existing_user.destroy
  end

  # Create a mock request environment
  env = {
    'REQUEST_METHOD' => 'POST',
    'PATH_INFO' => '/users',
    'QUERY_STRING' => '',
    'HTTP_HOST' => 'localhost:3000',
    'rack.input' => StringIO.new,
    'rack.errors' => StringIO.new
  }

  # Create request
  request = ActionDispatch::Request.new(env)
  
  # Create controller instance
  controller = Users::RegistrationsController.new
  controller.request = request
  controller.response = ActionDispatch::Response.new

  # Set up Devise mapping
  request.env["devise.mapping"] = Devise.mappings[:user]

  # Set up parameters
  params = ActionController::Parameters.new({
    user: user_params,
    terms: 'on',
    controller: 'users/registrations',
    action: 'create'
  })
  
  controller.params = params

  puts "  📝 Attempting to create user with controller..."
  
  # Call the create action directly
  result = controller.create

  puts "  ✅ Controller create method executed"
  
  # Check if user was created
  created_user = User.find_by(email: user_params[:email])
  if created_user
    puts "  ✅ User created successfully: #{created_user.full_name}"
    puts "  ✅ User tenant: #{created_user.tenant.name}"
    puts "  ✅ User role: #{created_user.role}"
    
    # Clean up
    puts "  🧹 Cleaning up test data"
    created_user.tenant.destroy
    created_user.destroy
  else
    puts "  ❌ User was not created"
  end

rescue => e
  puts "  ❌ Controller test error: #{e.message}"
  puts "  #{e.backtrace.first}"
end

puts "\n🌐 Testing with Rack App:"

begin
  # Create a Rack test session
  require 'rack/test'
  
  class TestApp
    include Rack::Test::Methods
    
    def app
      Rails.application
    end
  end
  
  test_app = TestApp.new
  
  # Make a POST request
  response = test_app.post '/users', {
    user: user_params,
    terms: 'on'
  }
  
  puts "  📡 Response status: #{response.status}"
  puts "  📡 Response headers: #{response.headers}"
  
  if response.status == 302
    puts "  ✅ Registration successful (redirect)"
  elsif response.status == 422
    puts "  ⚠️  Validation errors (422)"
  else
    puts "  ❌ Unexpected response: #{response.status}"
    puts "  Response body: #{response.body[0..500]}"
  end

rescue => e
  puts "  ❌ Rack test error: #{e.message}"
  puts "  #{e.backtrace.first}"
end

puts "\n" + "=" * 60
puts "🏁 Manual Registration Test Complete"
puts "=" * 60
