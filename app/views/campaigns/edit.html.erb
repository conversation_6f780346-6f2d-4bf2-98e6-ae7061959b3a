<% content_for :title, "Edit Campaign" %>
<div class="mb-8 w-7xl mx-auto px-4 sm:px-6 lg:px-8">
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-3xl font-bold text-gray-900 mb-2">Edit Campaign</h1>
      <p class="text-gray-600">Update your AI-powered marketing campaign settings.</p>
    </div>
    <div class="flex items-center space-x-4">
      <%= link_to campaign_path(@campaign),
          class: "inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors" do %>
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
        </svg>
        View Campaign
      <% end %>
      <%= link_to campaigns_path,
          class: "inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors" do %>
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        Back to Campaigns
      <% end %>
    </div>
  </div>
</div>

<!-- Campaign Status Badge -->
<div class="mb-6 w-7xl mx-auto px-4 sm:px-6 lg:px-8  ">
  <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full
    <%= case @campaign.status
        when 'active' then 'bg-green-100 text-green-800'
        when 'draft' then 'bg-gray-100 text-gray-800'
        when 'paused' then 'bg-yellow-100 text-yellow-800'
        when 'completed' then 'bg-blue-100 text-blue-800'
        when 'cancelled' then 'bg-red-100 text-red-800'
        else 'bg-gray-100 text-gray-800'
        end %>">
    Current Status: <%= @campaign.status.titleize %>
  </span>
</div>

<!-- Campaign Form -->
<div class="bg-white rounded-xl shadow-sm border border-gray-100 w-7xl mx-auto px-4 sm:px-6 lg:px-8">
  <%= form_with model: @campaign, local: true, class: "space-y-8" do |form| %>

    <!-- Form Header -->
    <div class="px-8 py-6 border-b border-gray-100">
      <h2 class="text-xl font-semibold text-gray-900">Campaign Details</h2>
      <p class="text-gray-600 mt-1">Update the settings for your marketing campaign.</p>
    </div>

    <!-- Error Messages -->
    <% if @campaign.errors.any? %>
      <div class="mx-8 bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="flex">
          <svg class="w-5 h-5 text-red-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">
              <%= pluralize(@campaign.errors.count, "error") %> prohibited this campaign from being saved:
            </h3>
            <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
              <% @campaign.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Form Content -->
    <div class="px-8 space-y-8">

      <!-- Basic Information -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Campaign Name -->
        <div class="lg:col-span-2">
          <%= form.label :name, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.text_field :name,
              class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",
              placeholder: "Enter campaign name (e.g., Holiday Sale 2024)" %>
        </div>

        <!-- Campaign Type -->
        <div>
          <%= form.label :campaign_type, "Campaign Type", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.select :campaign_type,
              options_for_select([
                ['Email Marketing', 'email'],
                ['Social Media', 'social'],
                ['SEO Optimization', 'seo'],
                ['Multi-Channel', 'multi_channel']
              ], @campaign.campaign_type),
              { prompt: 'Select campaign type' },
              { class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors" } %>
        </div>

        <!-- Status -->
        <div>
          <%= form.label :status, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.select :status,
              options_for_select([
                ['Draft', 'draft'],
                ['Active', 'active'],
                ['Paused', 'paused'],
                ['Completed', 'completed'],
                ['Cancelled', 'cancelled']
              ], @campaign.status),
              {},
              { class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors" } %>
        </div>
      </div>

      <!-- Description -->
      <div>
        <%= form.label :description, class: "block text-sm font-medium text-gray-700 mb-2" %>
        <%= form.text_area :description, rows: 4,
            class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors resize-none",
            placeholder: "Describe your campaign goals, target audience, and key messaging..." %>
      </div>

      <!-- Target Audience -->
      <div>
        <%= form.label :target_audience, class: "block text-sm font-medium text-gray-700 mb-2" %>
        <%= form.text_field :target_audience,
            class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",
            placeholder: "e.g., Existing customers aged 25-45 interested in technology" %>
      </div>

      <!-- Budget and Dates -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Budget -->
        <div>
          <%= form.label :budget_in_dollars, "Budget ($)", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span class="text-gray-500 sm:text-sm">$</span>
            </div>
            <%= form.number_field :budget_in_dollars, step: 0.01, min: 0,
                class: "w-full pl-8 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",
                placeholder: "0.00" %>
          </div>
        </div>

        <!-- Start Date -->
        <div>
          <%= form.label :start_date, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.date_field :start_date,
              class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors" %>
        </div>

        <!-- End Date -->
        <div>
          <%= form.label :end_date, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.date_field :end_date,
              class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors" %>
        </div>
      </div>

      <!-- Campaign Performance (if active) -->
      <% if @campaign.active? || @campaign.completed? %>
        <div class="bg-green-50 rounded-lg p-6 border border-green-200">
          <h3 class="text-lg font-semibold text-green-900 mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            Campaign Performance
          </h3>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-white rounded-lg p-4 border border-green-200">
              <div class="text-2xl font-bold text-green-900"><%= @campaign.progress_percentage %>%</div>
              <div class="text-sm text-green-700">Progress</div>
            </div>

            <div class="bg-white rounded-lg p-4 border border-green-200">
              <div class="text-2xl font-bold text-green-900">
                <% if @campaign.duration_in_days %>
                  <%= @campaign.duration_in_days %> days
                <% else %>
                  N/A
                <% end %>
              </div>
              <div class="text-sm text-green-700">Duration</div>
            </div>

            <div class="bg-white rounded-lg p-4 border border-green-200">
              <div class="text-2xl font-bold text-green-900">$<%= number_with_precision(@campaign.budget_in_dollars, precision: 0, delimiter: ',') %></div>
              <div class="text-sm text-green-700">Budget</div>
            </div>
          </div>
        </div>
      <% end %>
    </div>

    <!-- Form Footer -->
    <div class="px-8 py-6 bg-gray-50 border-t border-gray-100 rounded-b-xl">
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-600">
          <p>💡 <strong>Note:</strong> Changes to active campaigns may take a few minutes to take effect.</p>
        </div>

        <div class="flex items-center space-x-4">
          <%= link_to campaign_path(@campaign),
              class: "px-6 py-3 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors" do %>
            Cancel
          <% end %>

          <%= form.submit "Update Campaign",
              class: "px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200" %>
        </div>
      </div>
    </div>
  <% end %>
</div>

<!-- Campaign Actions -->
<div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
  <!-- Status Actions -->
  <div class="bg-white rounded-lg p-6 border border-gray-200">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">Campaign Actions</h3>
    <div class="space-y-3">
      <% if @campaign.can_be_activated? %>
        <%= button_to activate_campaign_path(@campaign),
            method: :patch,
            class: "w-full inline-flex items-center justify-center px-4 py-2 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors",
            form: { data: { turbo_confirm: "Are you sure you want to activate this campaign?" } } do %>
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4V8a3 3 0 016 0v2M7 16a3 3 0 006 0v-2"></path>
          </svg>
          Activate Campaign
        <% end %>
      <% elsif @campaign.active? %>
        <%= button_to pause_campaign_path(@campaign),
            method: :patch,
            class: "w-full inline-flex items-center justify-center px-4 py-2 bg-yellow-600 text-white font-medium rounded-lg hover:bg-yellow-700 transition-colors",
            form: { data: { turbo_confirm: "Are you sure you want to pause this campaign?" } } do %>
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          Pause Campaign
        <% end %>
      <% end %>

      <% if @campaign.active? || @campaign.paused? %>
        <%= button_to complete_campaign_path(@campaign),
            method: :patch,
            class: "w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors",
            form: { data: { turbo_confirm: "Are you sure you want to mark this campaign as completed?" } } do %>
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          Mark Complete
        <% end %>
      <% end %>
    </div>
  </div>

  <!-- Campaign Type Specific -->
  <div class="bg-white rounded-lg p-6 border border-gray-200">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">Campaign Details</h3>
    <div class="space-y-3">
      <% case @campaign.campaign_type %>
      <% when 'email' %>
        <% if @campaign.email_campaign %>
          <%= link_to "#", class: "w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
            Edit Email Content
          <% end %>
        <% else %>
          <%= link_to "#", class: "w-full inline-flex items-center justify-center px-4 py-2 bg-gray-600 text-white font-medium rounded-lg hover:bg-gray-700 transition-colors" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Setup Email Content
          <% end %>
        <% end %>
      <% when 'social' %>
        <% if @campaign.social_campaign %>
          <%= link_to "#", class: "w-full inline-flex items-center justify-center px-4 py-2 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
            </svg>
            Edit Social Content
          <% end %>
        <% else %>
          <%= link_to "#", class: "w-full inline-flex items-center justify-center px-4 py-2 bg-gray-600 text-white font-medium rounded-lg hover:bg-gray-700 transition-colors" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Setup Social Content
          <% end %>
        <% end %>
      <% when 'seo' %>
        <% if @campaign.seo_campaign %>
          <%= link_to "#", class: "w-full inline-flex items-center justify-center px-4 py-2 bg-purple-600 text-white font-medium rounded-lg hover:bg-purple-700 transition-colors" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            Edit SEO Strategy
          <% end %>
        <% else %>
          <%= link_to "#", class: "w-full inline-flex items-center justify-center px-4 py-2 bg-gray-600 text-white font-medium rounded-lg hover:bg-gray-700 transition-colors" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Setup SEO Strategy
          <% end %>
        <% end %>
      <% end %>
    </div>
  </div>

  <!-- Danger Zone -->
  <div class="bg-red-50 rounded-lg p-6 border border-red-200">
    <h3 class="text-lg font-semibold text-red-900 mb-4">Danger Zone</h3>
    <div class="space-y-3">
      <%= link_to campaign_path(@campaign),
          class: "w-full inline-flex items-center justify-center px-4 py-2 bg-red-600 text-white font-medium rounded-lg hover:bg-red-700 transition-colors",
          data: {
            turbo_method: :delete,
            turbo_confirm: "Are you sure you want to delete this campaign? This action cannot be undone."
          } do %>
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
        </svg>
        Delete Campaign
      <% end %>
    </div>
  </div>
</div>
